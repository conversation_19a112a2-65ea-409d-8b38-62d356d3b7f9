window.addEventListener('message', function(event) {
  const data = event.data;

  if (data.action === 'showMenu') {
    document.getElementById('void-menu').classList.remove('hidden');
    const name = data.playerName || 'Player';
    document.getElementById('player-name').innerText = name;

    // Hit Boxes fields
    document.getElementById('toggle-hitboxes').checked = data.displayBoxes || false;
    document.getElementById('toggle-npcs').checked = data.includeNPCs || false;
    document.getElementById('radius-select').value     = data.radius || '100';
    document.getElementById('boxsize-select').value    = data.boxSizeLevel || 'normal';

    // Interior Hold defaults
    document.getElementById('ped-count-select').value   = data.pedCount || '5';
    document.getElementById('ped-weapon-select').value  = data.pedWeapon || 'pistol';
    document.getElementById('location-select').value    = data.location || 'Airport';
  } 
  else if (data.action === 'hideMenu') {
    document.getElementById('void-menu').classList.add('hidden');
  }
});

// ==========================
// Hit Boxes Dropdown Logic
// ==========================
document.getElementById('hitboxes-btn').addEventListener('click', function() {
  document.getElementById('hitboxes-options').classList.toggle('hidden');
});

document.getElementById('turnon-btn').addEventListener('click', function() {
  const displayHitBoxes = document.getElementById('toggle-hitboxes').checked;
  const includeNPCs     = document.getElementById('toggle-npcs').checked;
  const radius          = document.getElementById('radius-select').value;
  const boxSizeLevel    = document.getElementById('boxsize-select').value;

  fetch(`https://${GetParentResourceName()}/turnOn`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json; charset=UTF-8' },
    body: JSON.stringify({
      displayHitBoxes: displayHitBoxes,
      includeNPCs: includeNPCs,
      radius: radius,
      boxSizeLevel: boxSizeLevel
    })
  });
});

document.getElementById('turnoff-btn').addEventListener('click', function() {
  fetch(`https://${GetParentResourceName()}/turnOff`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json; charset=UTF-8' },
    body: JSON.stringify({})
  });
});

document.getElementById('close-btn').addEventListener('click', function() {
  fetch(`https://${GetParentResourceName()}/closeMenu`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json; charset=UTF-8' },
    body: JSON.stringify({})
  });
});

// =============================
// Interior Hold: toggle dropdown
// =============================
document.getElementById('interior-btn').addEventListener('click', function() {
  document.getElementById('interior-options').classList.toggle('hidden');
});

// =============================
// Interior Hold: Activate button
// =============================
document.getElementById('activate-interior-btn').addEventListener('click', function() {
  // Read selected amount, weapon, and location
  const pedCount   = document.getElementById('ped-count-select').value;
  const pedWeapon  = document.getElementById('ped-weapon-select').value;
  const location   = document.getElementById('location-select').value;

  fetch(`https://${GetParentResourceName()}/activateInterior`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json; charset=UTF-8' },
    body: JSON.stringify({
      pedCount: parseInt(pedCount, 10),
      pedWeapon: pedWeapon,
      location: location
    })
  });
});
