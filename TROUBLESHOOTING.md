# Void Hitbox System - Troubleshooting Guide

## Menu Not Opening Issue

If the `/void` command doesn't open the menu, follow these steps:

### Step 1: Check OX_lib Installation
1. Make sure `ox_lib` is installed in your resources folder
2. Ensure `ox_lib` is started BEFORE this resource in your server.cfg:
   ```
   ensure ox_lib
   ensure void_syko_hitbox
   ```

### Step 2: Test OX_lib
Use the test command to check if OX_lib is working:
```
/voidtest
```

If you see "OX_lib is working!" then the library is loaded correctly.
If you see "OX_lib not found!" then you need to install/fix ox_lib.

### Step 3: Check Permissions
Make sure your Discord ID is in the authorized list:
- Use `/voidid` to get your Discord ID
- Check that it's added to both client.lua and server.lua AUTHORIZED_DISCORD_IDS tables
- Use `/voidrefresh` to refresh your permissions

### Step 4: Fallback Menu System
If OX_lib isn't working, the system will automatically use a chat-based menu:

1. Type `/void` - this will show the menu options in chat
2. Use these commands to control the system:
   - `/void1` - Toggle display hitboxes
   - `/void2` - Change radius
   - `/void3` - Change box size  
   - `/void4` - Enable/disable hitboxes

### Step 5: Console Errors
Check your client console (F8) for any errors:
- Look for ox_lib related errors
- Look for script errors in void_syko_hitbox

### Step 6: Resource Loading
Make sure the resource is properly loaded:
1. Check server console for loading messages
2. Use `/refresh` and `/ensure void_syko_hitbox` to reload
3. Check that fxmanifest.lua is correct

## Common Issues

### "You do not have permission"
- Use `/voidid` to check your Discord ID
- Make sure your Discord ID is in both client.lua and server.lua
- Use `/voidrefresh` to refresh permissions
- Check server console for authorization messages

### Menu appears but options don't work
- This usually means ox_lib is partially loaded
- Restart the resource: `/restart void_syko_hitbox`
- Check server console for errors

### Hitboxes not showing
1. Make sure you've enabled "Display Hit Boxes" in the menu
2. Make sure the hitbox system is enabled
3. Check that there are other players nearby (within radius)
4. Try different radius settings

### Performance Issues
1. Reduce the radius setting (try 50m instead of 100m)
2. Make sure you're not running too many other scripts
3. Check server performance

## Manual Configuration

If the menu system completely fails, you can manually edit the client.lua file:

```lua
-- At the top of client.lua, change these values:
local hitBoxesEnabled    = true   -- Enable the system
local displayBoxes       = true   -- Show visual boxes
local boxRadius          = 100.0  -- Detection radius
local boxSizeLevel       = "normal" -- Box size
```

## Getting Help

If none of these solutions work:

1. Check the server console for errors
2. Check the client console (F8) for errors
3. Make sure you have the latest version of ox_lib
4. Try restarting both ox_lib and void_syko_hitbox resources
5. Test on a clean server with minimal resources

## Working Confirmation

When everything is working correctly:
- `/void` opens a clean menu with 4 options
- You can toggle settings and see notifications
- Hitboxes appear as blue boxes around players
- Selected targets show as red boxes
- Left-click shoots at the selected target

## Alternative: Chat Commands

If you prefer chat commands over menus, you can use:
- `/void` - Show current settings
- `/void1` - Toggle visual display
- `/void2` - Change radius (opens input dialog)
- `/void3` - Change box size (opens input dialog)  
- `/void4` - Enable/disable system
