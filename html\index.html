<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Void Menu</title>
  <link rel="stylesheet" href="css/style.css" />
</head>
<body>
  <div id="void-menu" class="hidden">
    <div class="menu-container">
      <!-- Header with Logo and Title -->
      <div class="header">
        <img src="images/logo.png" alt="Logo" class="logo" />
        <h1>Void</h1>
      </div>

      <!-- Greeting -->
      <div class="greeting left">
        Hello, <span id="player-name"></span>
      </div>

      <!-- ============================= -->
      <!-- Hit Boxes Section Start      -->
      <!-- ============================= -->
      <div class="section">
        <button id="hitboxes-btn" class="primary-btn">Hit Boxes ▾</button>
        <div id="hitboxes-options" class="hidden">
          <label>
            <input type="checkbox" id="toggle-hitboxes" />
            Display Hit Boxes
          </label>
          <label>
            <input type="checkbox" id="toggle-npcs" />
            Include NPC Peds
          </label>
          <label for="radius-select">Radius:</label>
          <select id="radius-select">
            <option value="50">50 m</option>
            <option value="100" selected>100 m</option>
            <option value="150">150 m</option>
            <option value="200">200 m</option>
          </select>
          <label for="boxsize-select">Box Size:</label>
          <select id="boxsize-select">
            <option value="small">Small</option>
            <option value="normal" selected>Normal</option>
            <option value="big">Big</option>
            <option value="verybig">Very Big</option>
          </select>
          <button id="turnon-btn" class="primary-btn" style="margin-top: 10px;">
            Turn On
          </button>
          <button id="turnoff-btn" class="secondary-btn" style="margin-top: 10px;">
            Turn Off
          </button>
        </div>
      </div>
      <!-- ============================= -->
      <!-- Hit Boxes Section End        -->
      <!-- ============================= -->

      <!-- ============================= -->
      <!-- Interior Hold Section Start  -->
      <!-- ============================= -->
      <div class="section">
        <button id="interior-btn" class="primary-btn">Interior Hold ▾</button>
        <div id="interior-options" class="hidden">
          <label for="ped-count-select">Amount of Peds:</label>
          <select id="ped-count-select">
            <option value="5" selected>5</option>
            <option value="10">10</option>
            <option value="15">15</option>
            <option value="20">20</option>
          </select>

          <label for="ped-weapon-select">Ped Weapons:</label>
          <select id="ped-weapon-select">
            <option value="pistol" selected>Pistol</option>
            <option value="automatic">Automatic</option>
          </select>

          <label for="location-select">Location:</label>
          <select id="location-select">
            <option value="Airport" selected>Airport</option>
            <option value="Outside Right Hand">Outside Right Hand</option>
            <option value="Garage Right Hand">Garage Right Hand</option>
            <option value="Cluckin Bell">Cluckin Bell</option>
          </select>

          <button id="activate-interior-btn" class="primary-btn" style="margin-top: 10px;">
            Activate
          </button>
        </div>
      </div>
      <!-- ============================= -->
      <!-- Interior Hold Section End    -->
      <!-- ============================= -->

      <!-- Close Button -->
      <button id="close-btn" class="secondary-btn">Close</button>
    </div>
  </div>

  <script src="js/script.js"></script>
</body>
</html>
