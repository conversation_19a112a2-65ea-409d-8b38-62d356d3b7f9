-- Configuration: Add your Discord IDs here (same as client.lua)
local AUTHORIZED_DISCORD_IDS = {
    "846599992449302549", -- Replace with your Discord ID
    "987654321098765432", -- Add more Discord IDs as needed
    -- Add more IDs here
}

-- Function to get player's Discord ID
local function GetPlayerDiscordId(source)
    local identifiers = GetPlayerIdentifiers(source)

    for _, identifier in pairs(identifiers) do
        if string.sub(identifier, 1, 8) == "discord:" then
            return string.sub(identifier, 9)
        end
    end

    return nil
end

-- Function to check if player is authorized
local function IsPlayerAuthorized(discordId)
    if not discordId then
        return false
    end

    for _, authorizedId in pairs(AUTHORIZED_DISCORD_IDS) do
        if discordId == authorizedId then
            return true
        end
    end

    return false
end

-- Handle permission check request from client
RegisterNetEvent('void:checkPermission')
AddEventHandler('void:checkPermission', function()
    local source = source
    local discordId = GetPlayerDiscordId(source)
    local authorized = IsPlayerAuthorized(discordId)

    -- Send result back to client
    TriggerClientEvent('void:permissionResult', source, authorized, discordId)

    -- Debug logging (optional)
    if authorized then
        print(string.format("[VOID] Player %s (Discord: %s) authorized", GetPlayerName(source), discordId or "unknown"))
    else
        print(string.format("[VOID] Player %s (Discord: %s) NOT authorized", GetPlayerName(source), discordId or "unknown"))
    end
end)

-- Handle player connecting (auto-check permissions)
AddEventHandler('playerConnecting', function()
    local source = source

    -- Wait a bit for identifiers to be available
    Citizen.SetTimeout(5000, function()
        local discordId = GetPlayerDiscordId(source)
        local authorized = IsPlayerAuthorized(discordId)

        -- Send initial permission status
        TriggerClientEvent('void:permissionResult', source, authorized, discordId)
    end)
end)

-- Optional: Command to reload permissions (for admins)
RegisterCommand('voidreload', function(source, args, rawCommand)
    if source == 0 then -- Console only
        print("[VOID] Permission system reloaded")

        -- Refresh all connected players
        for _, playerId in pairs(GetPlayers()) do
            local discordId = GetPlayerDiscordId(tonumber(playerId))
            local authorized = IsPlayerAuthorized(discordId)
            TriggerClientEvent('void:permissionResult', tonumber(playerId), authorized, discordId)
        end
    end
end, true)

-- Optional: Command to check a player's Discord ID (console only)
RegisterCommand('voidcheck', function(source, args, rawCommand)
    if source == 0 and args[1] then -- Console only
        local targetId = tonumber(args[1])
        if targetId and GetPlayerName(targetId) then
            local discordId = GetPlayerDiscordId(targetId)
            local authorized = IsPlayerAuthorized(discordId)

            print(string.format("[VOID] Player %s (ID: %s) - Discord: %s - Authorized: %s",
                GetPlayerName(targetId), targetId, discordId or "unknown", authorized and "YES" or "NO"))
        else
            print("[VOID] Invalid player ID")
        end
    end
end, true)
