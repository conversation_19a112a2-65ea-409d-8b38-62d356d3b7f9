ESX = nil
TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

-- When a client types /void, they trigger this server event:
RegisterNetEvent('removeped:checkPermission')
AddEventHandler('removeped:checkPermission', function()
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)
    if not xPlayer then
        return
    end

    -- If group is exactly "owner", allow:
    if xPlayer.getGroup() == 'owner' then
        TriggerClientEvent('removeped:openMenu', src)
    else
        -- Otherwise, send a red chat message right away:
        TriggerClientEvent('chat:addMessage', src, {
            args = { '^1SYSTEM', 'You do not have permission to use this command.' }
        })
    end
end)

-- (Optional) placeholder for logging when toggling head-boxes:
RegisterNetEvent('removeped:toggleLog')
AddEventHandler('removeped:toggleLog', function(state)
    -- You can log to console or elsewhere if desired
end)
