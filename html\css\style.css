body {
  margin: 0;
  padding: 0;
  font-family: Arial, sans-serif;
}

.hidden {
  display: none;
}

#void-menu {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 9999;
}

.menu-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #0a1f3d;
  border-radius: 12px;
  width: 60vw;
  max-width: 700px;
  min-width: 300px;
  padding: 25px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
  color: white;
}

.header {
  display: flex;
  align-items: center;
}

.header .logo {
  width: 36px;
  height: 36px;
  margin-right: 10px;
}

.header h1 {
  margin: 0;
  font-size: 1.5em;
}

.greeting.left {
  margin: 15px 0;
  font-size: 1.1em;
  text-align: left;
  padding-left: 4px;
}

.section {
  margin-bottom: 15px;
}

.primary-btn {
  width: 100%;
  background: #1f4e79;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px;
  font-size: 1em;
  cursor: pointer;
  transition: background 0.2s;
}

.primary-btn:hover {
  background: #296aa3;
}

.secondary-btn {
  width: 100%;
  background: #333;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px;
  font-size: 1em;
  cursor: pointer;
  margin-top: 10px;
  transition: background 0.2s;
}

.secondary-btn:hover {
  background: #555;
}

#hitboxes-options,
#interior-options {
  margin-top: 10px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
}

#hitboxes-options label,
#interior-options label {
  display: block;
  margin-bottom: 8px;
  font-size: 0.95em;
}

#radius-select,
#boxsize-select,
#ped-count-select,
#ped-weapon-select {
  width: 100%;
  padding: 8px;
  font-size: 1em;
  border-radius: 4px;
  border: none;
  background: #1f4e79;
  color: white;
  margin-bottom: 10px;
}
