ESX = nil

-- State: Hit Boxes
local hitBoxesEnabled    = false
local displayBoxes       = false
local includeNPCs        = false
local boxRadius          = 100.0
local boxSizeLevel       = "normal"

-- Enhanced hitbox constants
local K            = 0.8  -- Increased for better visibility
local MIN_BOX_SIZE = 0.015  -- Slightly smaller minimum
local MAX_BOX_SIZE = 0.25   -- Slightly larger maximum

local BORDER_THICKNESS = 0.003  -- <PERSON><PERSON><PERSON> borders for better visibility
local TEXT_SCALE       = 0.4    -- Larger text
local SCREEN_CENTER    = { x = 0.5, y = 0.5 }
local SMOOTH_ALPHA     = 0.05   -- Faster smoothing for better responsiveness
local smoothedPos      = {}
local lastTargets      = {}     -- Cache for performance optimization

-- Enhanced bone targeting
local HEAD_BONE_INDEX = 31086  -- SKEL_Head
local NECK_BONE_INDEX = 39317  -- SKEL_Neck_1
local CHEST_BONE_INDEX = 24818 -- SKEL_Spine2

-- State: Interior Hold
local DEFAULT_PED_MODEL = "a_m_m_business_01"
local REL_GROUP_NAME    = "INTERIOR_HOSTILES"

-- Predefined locations (matching the dropdown keys)
local LOCATIONS = {
  ["Airport"] = {
    player = vector4(-1089.4799, -2718.4421, 0.8149, 247.7598),
    ped    = vector4(-1065.5074, -2720.7085, 0.8150, 127.0775)
  },
  ["Outside Right Hand"] = {
    player = vector4(-491.3281, 177.9162, 83.1576, 90.3292),
    ped    = vector4(-496.9584, 173.5700, 79.3147, 251.2533)
  },
  ["Garage Right Hand"] = {
    player = vector4(-789.1950, 326.2276, 85.7004, 287.2553),
    ped    = vector4(-805.2904, 305.2048, 86.0423, 111.2273)
  },
  ["Cluckin Bell"] = {
    player = vector4(-522.1928, -692.1403, 33.1679, 80.2321),
    ped    = vector4(-511.9488, -677.3790, 33.1800, 181.3109)
  }
}

-- Ensure ESX is loaded
Citizen.CreateThread(function()
  while not ESX do
    TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
    Citizen.Wait(100)
  end
end)

-- ================================
-- Helper Functions (Hit Boxes)
-- ================================
local function GetSizeMultiplier()
  if boxSizeLevel == "small" then return 0.75
  elseif boxSizeLevel == "big" then return 1.25
  elseif boxSizeLevel == "verybig" then return 1.5 end
  return 1.0
end

local function GetBoxSizeForDistance(dist)
  if dist <= 0 then return MAX_BOX_SIZE end
  if dist >= boxRadius then return MIN_BOX_SIZE end

  -- Improved scaling with exponential falloff for better visibility at range
  local normalizedDist = dist / boxRadius
  local size = MIN_BOX_SIZE + (MAX_BOX_SIZE - MIN_BOX_SIZE) * math.exp(-normalizedDist * 3.0)

  return math.max(MIN_BOX_SIZE, math.min(MAX_BOX_SIZE, size))
end

local function DrawTextOnScreen(text, x, y, scale, r, g, b, a)
  SetTextFont(4) -- Better font for readability
  SetTextProportional(1)
  SetTextScale(scale, scale)
  SetTextColour(r or 255, g or 255, b or 255, a or 255)
  SetTextCentre(true)
  SetTextOutline()
  SetTextDropShadow(2, 0, 0, 0, 255) -- Add drop shadow for better visibility
  BeginTextCommandDisplayText("STRING")
  AddTextComponentString(text)
  EndTextCommandDisplayText(x, y)
end

local function DrawEnhancedHitbox(x, y, boxSize, isTarget, targetType, serverId, dist)
  local half = boxSize / 2
  local left = x - half
  local right = x + half
  local top = y - half
  local bottom = y + half

  -- Color coding based on target type and selection
  local r, g, b, a = 0, 255, 0, 200 -- Default green
  local borderAlpha = 255

  if isTarget then
    r, g, b = 255, 0, 0 -- Red for selected target
    borderAlpha = 255
  elseif targetType == "player" then
    r, g, b = 0, 150, 255 -- Blue for players
  else
    r, g, b = 255, 165, 0 -- Orange for NPCs
  end

  -- Distance-based alpha for better depth perception
  local distanceAlpha = math.max(100, 255 - (dist * 2))
  a = math.min(a, distanceAlpha)
  borderAlpha = math.min(borderAlpha, distanceAlpha)

  -- Draw border with enhanced thickness
  local t = BORDER_THICKNESS
  DrawRect((left + right) / 2, top + (t / 2), boxSize, t, r, g, b, borderAlpha)
  DrawRect((left + right) / 2, bottom - (t / 2), boxSize, t, r, g, b, borderAlpha)
  DrawRect(left + (t / 2), (top + bottom) / 2, t, (bottom - top), r, g, b, borderAlpha)
  DrawRect(right - (t / 2), (top + bottom) / 2, t, (bottom - top), r, g, b, borderAlpha)

  -- Add corner indicators for selected target
  if isTarget then
    local cornerSize = t * 2
    local cornerLength = boxSize * 0.15
    -- Top-left corner
    DrawRect(left + (cornerLength / 2), top + (cornerSize / 2), cornerLength, cornerSize, r, g, b, borderAlpha)
    DrawRect(left + (cornerSize / 2), top + (cornerLength / 2), cornerSize, cornerLength, r, g, b, borderAlpha)
    -- Top-right corner
    DrawRect(right - (cornerLength / 2), top + (cornerSize / 2), cornerLength, cornerSize, r, g, b, borderAlpha)
    DrawRect(right - (cornerSize / 2), top + (cornerLength / 2), cornerSize, cornerLength, r, g, b, borderAlpha)
    -- Bottom-left corner
    DrawRect(left + (cornerLength / 2), bottom - (cornerSize / 2), cornerLength, cornerSize, r, g, b, borderAlpha)
    DrawRect(left + (cornerSize / 2), bottom - (cornerLength / 2), cornerSize, cornerLength, r, g, b, borderAlpha)
    -- Bottom-right corner
    DrawRect(right - (cornerLength / 2), bottom - (cornerSize / 2), cornerLength, cornerSize, r, g, b, borderAlpha)
    DrawRect(right - (cornerSize / 2), bottom - (cornerLength / 2), cornerSize, cornerLength, r, g, b, borderAlpha)
  end

  -- Enhanced text display
  local textY = top - 0.02
  local textColor = isTarget and {255, 255, 0} or {255, 255, 255} -- Yellow for target, white for others
  DrawTextOnScreen(tostring(serverId), (left + right) / 2, textY, TEXT_SCALE, textColor[1], textColor[2], textColor[3], math.min(255, distanceAlpha + 50))

  -- Distance indicator for selected target
  if isTarget then
    local distText = string.format("%.1fm", dist)
    DrawTextOnScreen(distText, (left + right) / 2, textY - 0.025, TEXT_SCALE * 0.8, 255, 255, 0, 200)
  end
end

local function GetBestBonePosition(ped)
  -- Try multiple bones for best targeting
  local headBone = GetPedBoneIndex(ped, HEAD_BONE_INDEX)
  local neckBone = GetPedBoneIndex(ped, NECK_BONE_INDEX)
  local chestBone = GetPedBoneIndex(ped, CHEST_BONE_INDEX)

  -- Prefer head, fallback to neck, then chest
  if headBone ~= -1 then
    return GetWorldPositionOfEntityBone(ped, headBone)
  elseif neckBone ~= -1 then
    return GetWorldPositionOfEntityBone(ped, neckBone)
  elseif chestBone ~= -1 then
    return GetWorldPositionOfEntityBone(ped, chestBone)
  else
    -- Fallback to entity coords with head offset
    local coords = GetEntityCoords(ped)
    return vector3(coords.x, coords.y, coords.z + 0.7)
  end
end

local function IsValidTarget(ped)
  if not DoesEntityExist(ped) or IsEntityDead(ped) then
    return false
  end

  -- Additional checks for NPCs
  if not IsPedAPlayer(ped) then
    -- Skip certain ped types that shouldn't be targeted
    local pedType = GetPedType(ped)
    if pedType == 28 then -- Animals
      return false
    end

    -- Skip peds in vehicles (driver seat)
    if IsPedInAnyVehicle(ped, false) and GetPedInVehicleSeat(GetVehiclePedIsIn(ped, false), -1) == ped then
      return false
    end
  end

  return true
end

local function GatherAllTargets(meCoords)
  local targets = {}
  local myPed = PlayerPedId()

  -- Players - more efficient gathering
  local activePlayers = GetActivePlayers()
  for i = 1, #activePlayers do
    local pid = activePlayers[i]
    if pid ~= PlayerId() then
      local targetPed = GetPlayerPed(pid)
      if IsValidTarget(targetPed) then
        local pedCoords = GetEntityCoords(targetPed)
        local dist = #(meCoords - pedCoords)
        if dist <= boxRadius and dist > 1.0 then -- Minimum distance to avoid self-targeting issues
          -- Line of sight check for better accuracy
          local hasLOS = HasEntityClearLosToEntity(myPed, targetPed, 17)
          if hasLOS then
            local bestBone = GetBestBonePosition(targetPed)
            targets[#targets + 1] = {
              type     = "player",
              id       = pid,
              serverId = GetPlayerServerId(pid),
              ped      = targetPed,
              coords   = pedCoords,
              dist     = dist,
              headWorld= bestBone,
              priority = 1 -- Players have higher priority
            }
          end
        end
      end
    end
  end

  -- NPCs - with better filtering and performance
  if includeNPCs then
    local handle, ped = FindFirstPed()
    local npcCount = 0
    local maxNPCs = 20 -- Limit NPCs for performance

    repeat
      local success
      success, ped = FindNextPed(handle)

      if DoesEntityExist(ped) and not IsPedAPlayer(ped) and IsValidTarget(ped) and npcCount < maxNPCs then
        local pedCoords = GetEntityCoords(ped)
        local dist = #(meCoords - pedCoords)
        if dist <= boxRadius and dist > 1.0 then
          -- Line of sight check for NPCs too
          local hasLOS = HasEntityClearLosToEntity(myPed, ped, 17)
          if hasLOS then
            local bestBone = GetBestBonePosition(ped)
            local netId = NetworkGetNetworkIdFromEntity(ped)
            targets[#targets + 1] = {
              type     = "npc",
              id       = netId,
              serverId = "NPC",
              ped      = ped,
              coords   = pedCoords,
              dist     = dist,
              headWorld= bestBone,
              priority = 2 -- NPCs have lower priority
            }
            npcCount = npcCount + 1
          end
        end
      end
    until not success
    EndFindPed(handle)
  end

  -- Sort targets by priority then distance
  table.sort(targets, function(a, b)
    if a.priority ~= b.priority then
      return a.priority < b.priority
    end
    return a.dist < b.dist
  end)

  return targets
end

-- =================================
-- OX_lib Menu Functions
-- =================================
local function OpenHitboxMenu()
  lib.registerMenu({
    id = 'hitbox_menu',
    title = 'Hit Boxes Configuration',
    position = 'top-right',
    options = {
      {
        label = 'Display Hit Boxes',
        description = 'Toggle visual hitbox display',
        checked = displayBoxes,
        args = { action = 'toggle_display' }
      },
      {
        label = 'Include NPCs',
        description = 'Include NPC peds in targeting',
        checked = includeNPCs,
        args = { action = 'toggle_npcs' }
      },
      {
        label = 'Radius: ' .. boxRadius .. 'm',
        description = 'Change detection radius',
        args = { action = 'change_radius' }
      },
      {
        label = 'Box Size: ' .. boxSizeLevel,
        description = 'Change hitbox size',
        args = { action = 'change_size' }
      },
      {
        label = hitBoxesEnabled and 'Turn Off' or 'Turn On',
        description = hitBoxesEnabled and 'Disable hitboxes' or 'Enable hitboxes',
        args = { action = 'toggle_hitboxes' }
      }
    }
  }, function(selected, scrollIndex, args)
    if args.action == 'toggle_display' then
      displayBoxes = not displayBoxes
      OpenHitboxMenu() -- Refresh menu
    elseif args.action == 'toggle_npcs' then
      includeNPCs = not includeNPCs
      OpenHitboxMenu() -- Refresh menu
    elseif args.action == 'change_radius' then
      local input = lib.inputDialog('Set Radius', {
        {type = 'number', label = 'Radius (meters)', default = boxRadius, min = 25, max = 500}
      })
      if input then
        boxRadius = input[1]
        OpenHitboxMenu() -- Refresh menu
      end
    elseif args.action == 'change_size' then
      local input = lib.inputDialog('Set Box Size', {
        {type = 'select', label = 'Size', options = {
          {value = 'small', label = 'Small'},
          {value = 'normal', label = 'Normal'},
          {value = 'big', label = 'Big'},
          {value = 'verybig', label = 'Very Big'}
        }, default = boxSizeLevel}
      })
      if input then
        boxSizeLevel = input[1]
        OpenHitboxMenu() -- Refresh menu
      end
    elseif args.action == 'toggle_hitboxes' then
      if hitBoxesEnabled then
        hitBoxesEnabled = false
        displayBoxes = false
        includeNPCs = false
        lib.notify({
          title = 'Void System',
          description = 'Hit Boxes Disabled',
          type = 'error'
        })
        TriggerServerEvent('removeped:toggleLog', false)
      else
        hitBoxesEnabled = true
        lib.notify({
          title = 'Void System',
          description = 'Hit Boxes Enabled',
          type = 'success'
        })
        TriggerServerEvent('removeped:toggleLog', true)
      end
      lib.hideMenu()
    end
  end)
  lib.showMenu('hitbox_menu')
end

-- Interior Hold Settings
local interiorSettings = {
  pedCount = 5,
  pedWeapon = 'pistol',
  location = 'Airport'
}

local function OpenInteriorMenu()
  lib.registerMenu({
    id = 'interior_menu',
    title = 'Interior Hold Configuration',
    position = 'top-right',
    options = {
      {
        label = 'Ped Count: ' .. interiorSettings.pedCount,
        description = 'Number of hostile peds to spawn',
        args = { action = 'set_count' }
      },
      {
        label = 'Weapon: ' .. (interiorSettings.pedWeapon == 'pistol' and 'Pistol' or 'Automatic'),
        description = 'Weapon type for hostile peds',
        args = { action = 'set_weapon' }
      },
      {
        label = 'Location: ' .. interiorSettings.location,
        description = 'Choose spawn location',
        args = { action = 'set_location' }
      },
      {
        label = 'Start Interior Hold',
        description = 'Begin the interior hold challenge',
        args = { action = 'start_interior' }
      }
    }
  }, function(selected, scrollIndex, args)
    if args.action == 'set_count' then
      local input = lib.inputDialog('Set Ped Count', {
        {type = 'select', label = 'Amount', options = {
          {value = 5, label = '5 Peds'},
          {value = 10, label = '10 Peds'},
          {value = 15, label = '15 Peds'},
          {value = 20, label = '20 Peds'}
        }, default = interiorSettings.pedCount}
      })
      if input then
        interiorSettings.pedCount = input[1]
        OpenInteriorMenu() -- Refresh menu
      end
    elseif args.action == 'set_weapon' then
      local input = lib.inputDialog('Set Weapon Type', {
        {type = 'select', label = 'Weapon', options = {
          {value = 'pistol', label = 'Pistol'},
          {value = 'automatic', label = 'Automatic'}
        }, default = interiorSettings.pedWeapon}
      })
      if input then
        interiorSettings.pedWeapon = input[1]
        OpenInteriorMenu() -- Refresh menu
      end
    elseif args.action == 'set_location' then
      local input = lib.inputDialog('Set Location', {
        {type = 'select', label = 'Location', options = {
          {value = 'Airport', label = 'Airport'},
          {value = 'Outside Right Hand', label = 'Outside Right Hand'},
          {value = 'Garage Right Hand', label = 'Garage Right Hand'},
          {value = 'Cluckin Bell', label = 'Cluckin Bell'}
        }, default = interiorSettings.location}
      })
      if input then
        interiorSettings.location = input[1]
        OpenInteriorMenu() -- Refresh menu
      end
    elseif args.action == 'start_interior' then
      TriggerEvent('void:activateInterior', interiorSettings)
      lib.hideMenu()
    end
  end)
  lib.showMenu('interior_menu')
end

local function OpenMainMenu()
  lib.registerMenu({
    id = 'void_main_menu',
    title = 'Void Menu - ' .. GetPlayerName(PlayerId()),
    position = 'top-right',
    options = {
      {
        label = 'Hit Boxes',
        description = 'Configure and control hitbox system',
        args = { action = 'hitboxes' }
      },
      {
        label = 'Interior Hold',
        description = 'Start interior hold challenge',
        args = { action = 'interior' }
      }
    }
  }, function(selected, scrollIndex, args)
    if args.action == 'hitboxes' then
      OpenHitboxMenu()
    elseif args.action == 'interior' then
      OpenInteriorMenu()
    end
  end)
  lib.showMenu('void_main_menu')
end

-- =================================
-- /void Command (client-side only)
-- =================================
RegisterCommand('void', function()
  if not ESX or not ESX.GetPlayerData() then return end
  local playerData = ESX.GetPlayerData()

  if playerData.group and playerData.group == 'owner' then
    -- Owner: open OX_lib menu
    lib.notify({
      title = 'Void System',
      description = 'Menu opened',
      type = 'inform'
    })
    OpenMainMenu()
  else
    -- Not owner: no permission
    lib.notify({
      title = 'Void System',
      description = 'You do not have permission to use this command',
      type = 'error'
    })
  end
end, false)

-- =================================
-- Event Handlers
-- =================================

-- =================================
-- Interior Hold: NUI Callback
-- =================================
-- Initialize custom relationship group
Citizen.CreateThread(function()
  AddRelationshipGroup(REL_GROUP_NAME)
  SetRelationshipBetweenGroups(0, GetHashKey(REL_GROUP_NAME), GetHashKey(REL_GROUP_NAME))
  SetRelationshipBetweenGroups(5, GetHashKey(REL_GROUP_NAME), GetHashKey("PLAYER"))
end)

-- Helper: load a ped model
local function LoadModel(modelName)
  local modelHash = GetHashKey(modelName)
  if not IsModelInCdimage(modelHash) or not IsModelValid(modelHash) then
    return false
  end
  RequestModel(modelHash)
  local timeout = 0
  while not HasModelLoaded(modelHash) and timeout < 10000 do
    Citizen.Wait(10)
    timeout = timeout + 10
  end
  return HasModelLoaded(modelHash)
end

RegisterNetEvent('void:activateInterior')
AddEventHandler('void:activateInterior', function(data)
  local me = PlayerPedId()

  -- Read chosen values or use defaults
  local count      = tonumber(data.pedCount) or 5
  local weaponType = data.pedWeapon or "pistol"
  local location   = data.location or "Airport"

  if not LOCATIONS[location] then
    location = "Airport"
  end
  local coords = LOCATIONS[location]

  -- Debug notification
  lib.notify({
    title = 'Interior Hold',
    description = string.format('Starting: %d peds, %s weapons at %s', count, weaponType, location),
    type = 'inform'
  })

  -- Teleport and freeze player
  SetEntityCoords(me, coords.player.x, coords.player.y, coords.player.z, false, false, false, true)
  SetEntityHeading(me, coords.player.w)
  FreezeEntityPosition(me, true)

  Citizen.CreateThread(function()
    while not ESX do
      Citizen.Wait(100)
    end

    local function CountdownNotify(msg)
      lib.notify({
        title = 'Interior Hold',
        description = msg,
        type = 'inform',
        duration = 1000
      })
    end

    CountdownNotify("3")
    Citizen.Wait(1000)
    CountdownNotify("2")
    Citizen.Wait(1000)
    CountdownNotify("1")
    Citizen.Wait(1000)
    CountdownNotify("GO!")

    -- Unfreeze after countdown
    FreezeEntityPosition(me, false)

    -- Pick weapon hash
    local weaponHash = GetHashKey(
      weaponType == "automatic" and "WEAPON_COMBATPDW" or "WEAPON_PISTOL50"
    )

    local spawnedPeds = {}

    -- Spawn each ped
    for i = 1, count do
      if LoadModel(DEFAULT_PED_MODEL) then
        local spawnedPed = CreatePed(
          4,
          GetHashKey(DEFAULT_PED_MODEL),
          coords.ped.x, coords.ped.y, coords.ped.z,
          coords.ped.w,
          true, true
        )
        -- Low health so two headshots kill
        SetEntityMaxHealth(spawnedPed, 100)
        SetEntityHealth(spawnedPed, 100)

        -- Relationship & hostility
        SetPedRelationshipGroupHash(spawnedPed, GetHashKey(REL_GROUP_NAME))
        SetPedAsEnemy(spawnedPed, true)

        -- Give weapon + accuracy
        GiveWeaponToPed(spawnedPed, weaponHash, 250, false, true)
        SetPedAccuracy(spawnedPed, 75)

        TaskCombatPed(spawnedPed, me, 0, 16)
        spawnedPeds[#spawnedPeds + 1] = spawnedPed

        SetModelAsNoLongerNeeded(GetHashKey(DEFAULT_PED_MODEL))
        Citizen.Wait(100)
      else
        -- ── Debug if model fails to load ────────────────────────────────
        TriggerEvent('chat:addMessage', {
          args = { '^1ERROR', 'Failed to load model: ' .. DEFAULT_PED_MODEL }
        })
        -- ────────────────────────────────────────────────────────────────
      end
    end

    -- Monitor for win/lose
    Citizen.CreateThread(function()
      while true do
        Citizen.Wait(500)

        if IsEntityDead(me) then
          lib.notify({
            title = 'Interior Hold',
            description = 'You lost the interior hold!',
            type = 'error',
            duration = 5000
          })
          break
        end

        local aliveCount = 0
        for _, ped in ipairs(spawnedPeds) do
          if DoesEntityExist(ped) and not IsEntityDead(ped) then
            aliveCount = aliveCount + 1
          end
        end

        if aliveCount == 0 then
          lib.notify({
            title = 'Interior Hold',
            description = 'You won the interior hold!',
            type = 'success',
            duration = 5000
          })
          break
        end
      end

      -- Cleanup leftover peds
      Citizen.Wait(1000)
      for _, ped in ipairs(spawnedPeds) do
        if DoesEntityExist(ped) then
          DeletePed(ped)
        end
      end
    end)
  end)
end)

-- =================================
-- Draw Loop: Head Boxes & Auto Headshots
-- =================================
Citizen.CreateThread(function()
  local frameCounter = 0
  while true do
    -- Performance optimization: reduce update frequency when not many targets
    local waitTime = hitBoxesEnabled and 0 or 100
    Citizen.Wait(waitTime)

    if hitBoxesEnabled then
      frameCounter = frameCounter + 1
      local me         = PlayerPedId()
      local myCoords   = GetEntityCoords(me)
      local shooterHead = GetWorldPositionOfEntityBone(me, GetPedBoneIndex(me, HEAD_BONE_INDEX))

      -- Only gather targets every few frames for performance
      local rawTargets = {}
      if frameCounter % 2 == 0 then -- Update targets every 2 frames
        rawTargets = GatherAllTargets(myCoords)
      else
        -- Use cached targets if available
        rawTargets = lastTargets or {}
      end
      lastTargets = rawTargets
      local gathered   = {}

      for _, info in ipairs(rawTargets) do
        local headW = info.headWorld
        -- Better Z-offset calculation based on distance and target type
        local zOffset = info.type == "player" and 0.05 or 0.08
        zOffset = zOffset + (info.dist * 0.002) -- Smaller distance-based offset

        local onScreen, rawX, rawY = World3dToScreen2d(
          headW.x, headW.y, headW.z + zOffset
        )

        -- Better fallback positioning
        if not onScreen then
          local fallback = vector3(info.coords.x, info.coords.y, info.coords.z + 0.8)
          onScreen, rawX, rawY = World3dToScreen2d(
            fallback.x, fallback.y, fallback.z
          )
        end

        if onScreen and rawX > 0 and rawX < 1 and rawY > 0 and rawY < 1 then -- Ensure on screen
          local key = info.type .. "_" .. tostring(info.id)
          if not smoothedPos[key] then
            smoothedPos[key] = { x = rawX, y = rawY }
          else
            local old = smoothedPos[key]
            -- Improved smoothing with distance-based responsiveness
            local responsiveness = math.max(0.02, SMOOTH_ALPHA * (1 + info.dist * 0.01))
            local newX = (old.x * (1 - responsiveness)) + (rawX * responsiveness)
            local newY = (old.y * (1 - responsiveness)) + (rawY * responsiveness)
            smoothedPos[key] = { x = newX, y = newY }
          end
          gathered[key] = {
            x         = smoothedPos[key].x,
            y         = smoothedPos[key].y,
            dist      = info.dist,
            headWorld = headW,
            serverId  = info.serverId,
            targetType = info.type,
            priority  = info.priority
          }
        end
      end

      -- Enhanced target selection with priority weighting
      local bestKey, bestScore = nil, -1
      local crosshairTolerance = 0.05 -- Slightly larger tolerance for easier targeting

      for key, info in pairs(gathered) do
        local baseSize = GetBoxSizeForDistance(info.dist)
        local sizeMult = GetSizeMultiplier()
        local boxSize  = baseSize * sizeMult
        local half     = boxSize / 2
        local left   = info.x - half
        local right  = info.x + half
        local top    = info.y - half
        local bottom = info.y + half

        -- Check if crosshair is within hitbox (with tolerance)
        if SCREEN_CENTER.x >= (left - crosshairTolerance) and SCREEN_CENTER.x <= (right + crosshairTolerance)
           and SCREEN_CENTER.y >= (top - crosshairTolerance) and SCREEN_CENTER.y <= (bottom + crosshairTolerance) then

          -- Calculate selection score (lower is better)
          local distanceFromCenter = math.sqrt(
            math.pow(SCREEN_CENTER.x - info.x, 2) +
            math.pow(SCREEN_CENTER.y - info.y, 2)
          )
          local score = (info.dist * 0.1) + (distanceFromCenter * 100) + (info.priority * 10)

          if bestScore == -1 or score < bestScore then
            bestScore = score
            bestKey = key
          end
        end
      end

      -- Enhanced visual display
      if displayBoxes then
        for key, info in pairs(gathered) do
          local baseSize = GetBoxSizeForDistance(info.dist)
          local sizeMult = GetSizeMultiplier()
          local boxSize  = baseSize * sizeMult
          local isTarget = (key == bestKey)

          DrawEnhancedHitbox(info.x, info.y, boxSize, isTarget, info.targetType, info.serverId, info.dist)
        end
      end

      -- Enhanced shooting system
      if bestKey and IsControlJustPressed(0, 24) then
        local info = gathered[bestKey]
        if info and info.dist <= boxRadius then
          local weaponHash = GetSelectedPedWeapon(me)

          -- Ensure we have a valid weapon
          if not weaponHash or weaponHash == GetHashKey("WEAPON_UNARMED") then
            weaponHash = GetHashKey("WEAPON_PISTOL") -- Default to pistol
          end

          -- Enhanced bullet trajectory with slight randomization for realism
          local targetX = info.headWorld.x + (math.random(-5, 5) * 0.001)
          local targetY = info.headWorld.y + (math.random(-5, 5) * 0.001)
          local targetZ = info.headWorld.z + (math.random(-2, 2) * 0.001)

          ShootSingleBulletBetweenCoords(
            shooterHead.x, shooterHead.y, shooterHead.z,
            targetX, targetY, targetZ,
            999,   -- High damage for headshots
            false, -- Not audible to avoid spam
            weaponHash,
            me,
            true,  -- networked
            false, -- p9
            -1.0   -- speed
          )

          -- Visual feedback for successful shot
          lib.notify({
            title = 'Hitbox System',
            description = string.format('Target hit: %s (%.1fm)', info.serverId, info.dist),
            type = 'success',
            duration = 1500
          })
        end
      end
    end
  end
end)


