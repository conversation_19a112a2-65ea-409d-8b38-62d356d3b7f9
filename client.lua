ESX = nil

-- State: Hit Boxes
local hitBoxesEnabled    = false
local displayBoxes       = false
local includeNPCs        = false
local boxRadius          = 100.0
local boxSizeLevel       = "normal"

-- Inverse‐distance constants
local K            = 0.5
local MIN_BOX_SIZE = 0.02
local MAX_BOX_SIZE = 0.2

local BORDER_THICKNESS = 0.002
local TEXT_SCALE       = 0.35
local SCREEN_CENTER    = { x = 0.5, y = 0.5 }
local SMOOTH_ALPHA     = 0.01
local smoothedPos      = {}

-- State: Interior Hold
local DEFAULT_PED_MODEL = "a_m_m_business_01"
local REL_GROUP_NAME    = "INTERIOR_HOSTILES"

-- Predefined locations (matching the dropdown keys)
local LOCATIONS = {
  ["Airport"] = {
    player = vector4(-1089.4799, -2718.4421, 0.8149, 247.7598),
    ped    = vector4(-1065.5074, -2720.7085, 0.8150, 127.0775)
  },
  ["Outside Right Hand"] = {
    player = vector4(-491.3281, 177.9162, 83.1576, 90.3292),
    ped    = vector4(-496.9584, 173.5700, 79.3147, 251.2533)
  },
  ["Garage Right Hand"] = {
    player = vector4(-789.1950, 326.2276, 85.7004, 287.2553),
    ped    = vector4(-805.2904, 305.2048, 86.0423, 111.2273)
  },
  ["Cluckin Bell"] = {
    player = vector4(-522.1928, -692.1403, 33.1679, 80.2321),
    ped    = vector4(-511.9488, -677.3790, 33.1800, 181.3109)
  }
}

-- Ensure ESX is loaded
Citizen.CreateThread(function()
  while not ESX do
    TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
    Citizen.Wait(100)
  end
end)

-- ================================
-- Helper Functions (Hit Boxes)
-- ================================
local function GetSizeMultiplier()
  if boxSizeLevel == "small" then return 0.75
  elseif boxSizeLevel == "big" then return 1.25
  elseif boxSizeLevel == "verybig" then return 1.5 end
  return 1.0
end

local function GetBoxSizeForDistance(dist)
  if dist <= 0 then return MAX_BOX_SIZE end
  if dist >= boxRadius then return MIN_BOX_SIZE end
  local size = K / dist
  if size > MAX_BOX_SIZE then size = MAX_BOX_SIZE end
  if size < MIN_BOX_SIZE then size = MIN_BOX_SIZE end
  return size
end

local function DrawTextOnScreen(text, x, y, scale)
  SetTextFont(0)
  SetTextProportional(1)
  SetTextScale(scale, scale)
  SetTextColour(0, 255, 0, 255)
  SetTextCentre(true)
  SetTextOutline()
  BeginTextCommandDisplayText("STRING")
  AddTextComponentString(text)
  EndTextCommandDisplayText(x, y)
end

local function GatherAllTargets(meCoords)
  local targets = {}
  -- Players
  for _, pid in ipairs(GetActivePlayers()) do
    if pid ~= PlayerId() then
      local targetPed = GetPlayerPed(pid)
      if DoesEntityExist(targetPed) and not IsEntityDead(targetPed) then
        local pedCoords = GetEntityCoords(targetPed)
        local dist = #(meCoords - pedCoords)
        if dist <= boxRadius then
          local headBoneIdx = GetPedBoneIndex(targetPed, 31086)
          local headWorld   = GetWorldPositionOfEntityBone(targetPed, headBoneIdx)
          targets[#targets + 1] = {
            type     = "player",
            id       = pid,
            serverId = GetPlayerServerId(pid),
            ped      = targetPed,
            coords   = pedCoords,
            dist     = dist,
            headWorld= headWorld
          }
        end
      end
    end
  end
  -- NPCs
  if includeNPCs then
    local handle, ped = FindFirstPed()
    local success
    repeat
      success = true
      if DoesEntityExist(ped) and not IsPedAPlayer(ped) and not IsEntityDead(ped) then
        local pedCoords = GetEntityCoords(ped)
        local dist = #(meCoords - pedCoords)
        if dist <= boxRadius then
          local headBoneIdx = GetPedBoneIndex(ped, 31086)
          local headWorld   = GetWorldPositionOfEntityBone(ped, headBoneIdx)
          local netId = NetworkGetNetworkIdFromEntity(ped)
          targets[#targets + 1] = {
            type     = "npc",
            id       = netId,
            serverId = "NPC",
            ped      = ped,
            coords   = pedCoords,
            dist     = dist,
            headWorld= headWorld
          }
        end
      end
      success, ped = FindNextPed(handle)
    until not success
    EndFindPed(handle)
  end
  return targets
end

-- =================================
-- /void Command (client-side only)
-- =================================
RegisterCommand('void', function()
  if not ESX or not ESX.GetPlayerData() then return end
  local playerData = ESX.GetPlayerData()

  if playerData.group and playerData.group == 'owner' then
    -- Owner: open NUI
    TriggerEvent('chat:addMessage', {
      args = { '^1SYSTEM', 'Void menu opened.' }
    })
    local playerName = GetPlayerName(PlayerId())
    SetNuiFocus(true, true)
    SendNUIMessage({
      action        = 'showMenu',
      playerName    = playerName,
      displayBoxes  = displayBoxes,
      includeNPCs   = includeNPCs,
      radius        = boxRadius,
      boxSizeLevel  = boxSizeLevel,
      pedCount      = 5,         -- default
      pedWeapon     = 'pistol',  -- default
      location      = 'Airport'  -- default
    })
  else
    -- Not owner: no permission
    TriggerEvent('chat:addMessage', {
      args = { '^1SYSTEM', 'You do not have permission to use this command.' }
    })
  end
end, false)

-- =================================
-- Hit Boxes: NUI Callbacks
-- =================================
RegisterNUICallback('turnOn', function(data, cb)
  displayBoxes    = data.displayHitBoxes
  includeNPCs     = data.includeNPCs
  boxRadius       = tonumber(data.radius) or 100.0
  boxSizeLevel    = data.boxSizeLevel or "normal"
  hitBoxesEnabled = true

  SetNuiFocus(false, false)
  SendNUIMessage({ action = 'hideMenu' })

  TriggerEvent('chat:addMessage', {
    args = { '^1SYSTEM', 'Hit Boxes On' }
  })

  TriggerServerEvent('removeped:toggleLog', true)
  cb('ok')
end)

RegisterNUICallback('turnOff', function(data, cb)
  hitBoxesEnabled = false
  displayBoxes    = false
  includeNPCs     = false

  SetNuiFocus(false, false)
  SendNUIMessage({ action = 'hideMenu' })

  TriggerEvent('chat:addMessage', {
    args = { '^1SYSTEM', 'Hit Boxes Off' }
  })

  TriggerServerEvent('removeped:toggleLog', false)
  cb('ok')
end)

-- =================================
-- Interior Hold: NUI Callback
-- =================================
-- Initialize custom relationship group
Citizen.CreateThread(function()
  AddRelationshipGroup(REL_GROUP_NAME)
  SetRelationshipBetweenGroups(0, GetHashKey(REL_GROUP_NAME), GetHashKey(REL_GROUP_NAME))
  SetRelationshipBetweenGroups(5, GetHashKey(REL_GROUP_NAME), GetHashKey("PLAYER"))
end)

-- Helper: load a ped model
local function LoadModel(modelName)
  local modelHash = GetHashKey(modelName)
  if not IsModelInCdimage(modelHash) or not IsModelValid(modelHash) then
    return false
  end
  RequestModel(modelHash)
  local timeout = 0
  while not HasModelLoaded(modelHash) and timeout < 10000 do
    Citizen.Wait(10)
    timeout = timeout + 10
  end
  return HasModelLoaded(modelHash)
end

RegisterNUICallback('activateInterior', function(data, cb)
  local me = PlayerPedId()

  -- Read chosen values or use defaults
  local count      = tonumber(data.pedCount) or 5
  local weaponType = data.pedWeapon or "pistol"
  local location   = data.location or "Airport"

  if not LOCATIONS[location] then
    location = "Airport"
  end
  local coords = LOCATIONS[location]

  -- Immediately remove NUI focus
  SetNuiFocus(false, false)
  SendNUIMessage({ action = 'hideMenu' })

  -- ── DEBUG: confirm what arrived ─────────────────────────────────────
  TriggerEvent('chat:addMessage', {
    args = {
      '^5[DEBUG]',
      ("Count=%s, Weapon=%s, Location=%s"):format(
        tostring(count),
        tostring(weaponType),
        tostring(location)
      )
    }
  })
  -- ────────────────────────────────────────────────────────────────────

  -- Teleport and freeze player
  SetEntityCoords(me, coords.player.x, coords.player.y, coords.player.z, false, false, false, true)
  SetEntityHeading(me, coords.player.w)
  FreezeEntityPosition(me, true)

  Citizen.CreateThread(function()
    while not ESX do
      Citizen.Wait(100)
    end

    local function ChatPrint(msg, colorTag)
      TriggerEvent('chat:addMessage', {
        args = { (colorTag or '^4') .. msg }
      })
    end

    ChatPrint("3", '^4')
    Citizen.Wait(1000)
    ChatPrint("2", '^4')
    Citizen.Wait(1000)
    ChatPrint("1", '^4')
    Citizen.Wait(1000)
    ChatPrint("GO", '^4')

    -- Unfreeze after countdown
    FreezeEntityPosition(me, false)

    -- Pick weapon hash
    local weaponHash = GetHashKey(
      weaponType == "automatic" and "WEAPON_COMBATPDW" or "WEAPON_PISTOL50"
    )

    local spawnedPeds = {}

    -- Spawn each ped
    for i = 1, count do
      if LoadModel(DEFAULT_PED_MODEL) then
        local spawnedPed = CreatePed(
          4,
          GetHashKey(DEFAULT_PED_MODEL),
          coords.ped.x, coords.ped.y, coords.ped.z,
          coords.ped.w,
          true, true
        )
        -- Low health so two headshots kill
        SetEntityMaxHealth(spawnedPed, 100)
        SetEntityHealth(spawnedPed, 100)

        -- Relationship & hostility
        SetPedRelationshipGroupHash(spawnedPed, GetHashKey(REL_GROUP_NAME))
        SetPedAsEnemy(spawnedPed, true)

        -- Give weapon + accuracy
        GiveWeaponToPed(spawnedPed, weaponHash, 250, false, true)
        SetPedAccuracy(spawnedPed, 75)

        TaskCombatPed(spawnedPed, me, 0, 16)
        spawnedPeds[#spawnedPeds + 1] = spawnedPed

        SetModelAsNoLongerNeeded(GetHashKey(DEFAULT_PED_MODEL))
        Citizen.Wait(100)
      else
        -- ── Debug if model fails to load ────────────────────────────────
        TriggerEvent('chat:addMessage', {
          args = { '^1ERROR', 'Failed to load model: ' .. DEFAULT_PED_MODEL }
        })
        -- ────────────────────────────────────────────────────────────────
      end
    end

    -- Monitor for win/lose
    Citizen.CreateThread(function()
      while true do
        Citizen.Wait(500)

        if IsEntityDead(me) then
          TriggerEvent('chat:addMessage', {
            args = { '^1You lost the interior hold.' }
          })
          break
        end

        local aliveCount = 0
        for _, ped in ipairs(spawnedPeds) do
          if DoesEntityExist(ped) and not IsEntityDead(ped) then
            aliveCount = aliveCount + 1
          end
        end

        if aliveCount == 0 then
          TriggerEvent('chat:addMessage', {
            args = { '^2You won the interior hold.' }
          })
          break
        end
      end

      -- Cleanup leftover peds
      Citizen.Wait(1000)
      for _, ped in ipairs(spawnedPeds) do
        if DoesEntityExist(ped) then
          DeletePed(ped)
        end
      end
    end)
  end)

  cb('ok')
end)

-- =================================
-- Draw Loop: Head Boxes & Auto Headshots
-- =================================
Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0)
    if hitBoxesEnabled then
      local me         = PlayerPedId()
      local myCoords   = GetEntityCoords(me)
      local shooterHead = GetWorldPositionOfEntityBone(me, GetPedBoneIndex(me, 31086))

      local rawTargets = GatherAllTargets(myCoords)
      local gathered   = {}

      for _, info in ipairs(rawTargets) do
        local headW = info.headWorld
        local zOffset = 0.1 + (info.dist * 0.005)
        local onScreen, rawX, rawY = World3dToScreen2d(
          headW.x, headW.y, headW.z + zOffset
        )
        if not onScreen then
          local fallback = vector3(info.coords.x, info.coords.y, info.coords.z + 1.0)
          onScreen, rawX, rawY = World3dToScreen2d(
            fallback.x, fallback.y, fallback.z
          )
        end
        if onScreen then
          local key = info.type .. "_" .. tostring(info.id)
          if not smoothedPos[key] then
            smoothedPos[key] = { x = rawX, y = rawY }
          else
            local old = smoothedPos[key]
            local newX = (old.x * SMOOTH_ALPHA) + (rawX * (1 - SMOOTH_ALPHA))
            local newY = (old.y * SMOOTH_ALPHA) + (rawY * (1 - SMOOTH_ALPHA))
            smoothedPos[key] = { x = newX, y = newY }
          end
          gathered[key] = {
            x         = smoothedPos[key].x,
            y         = smoothedPos[key].y,
            dist      = info.dist,
            headWorld = headW,
            serverId  = info.serverId
          }
        end
      end

      local bestKey, bestDist = nil, boxRadius + 1
      for key, info in pairs(gathered) do
        local baseSize = GetBoxSizeForDistance(info.dist)
        local sizeMult = GetSizeMultiplier()
        local boxSize  = baseSize * sizeMult
        local half     = boxSize / 2
        local left   = info.x - half
        local right  = info.x + half
        local top    = info.y - half
        local bottom = info.y + half

        if SCREEN_CENTER.x >= left and SCREEN_CENTER.x <= right
           and SCREEN_CENTER.y >= top and SCREEN_CENTER.y <= bottom then
          if info.dist < bestDist then
            bestDist = info.dist
            bestKey  = key
          end
        end
      end

      if displayBoxes then
        for key, info in pairs(gathered) do
          local baseSize = GetBoxSizeForDistance(info.dist)
          local sizeMult = GetSizeMultiplier()
          local boxSize  = baseSize * sizeMult
          local half     = boxSize / 2
          local left   = info.x - half
          local right  = info.x + half
          local top    = info.y - half
          local bottom = info.y + half

          local r, g, b, a = 0, 255, 0, 255
          if key == bestKey then
            r, g, b, a = 255, 0, 0, 255
          end

          local t = BORDER_THICKNESS
          DrawRect((left + right) / 2, top + (t / 2),    boxSize, t,    r, g, b, a)
          DrawRect((left + right) / 2, bottom - (t / 2), boxSize, t,    r, g, b, a)
          DrawRect(left + (t / 2),      (top + bottom) / 2, t, (bottom - top), r, g, b, a)
          DrawRect(right - (t / 2),     (top + bottom) / 2, t, (bottom - top), r, g, b, a)

          local textY = top - 0.015
          DrawTextOnScreen(tostring(info.serverId), (left + right) / 2, textY, TEXT_SCALE)
        end
      end

      if bestKey and IsControlJustPressed(0, 24) then
        local info = gathered[bestKey]
        if info and info.dist <= boxRadius then
          local weaponHash = GetSelectedPedWeapon(me) or GetHashKey("WEAPON_UNARMED")
          ShootSingleBulletBetweenCoords(
            shooterHead.x, shooterHead.y, shooterHead.z,
            info.headWorld.x, info.headWorld.y, info.headWorld.z,
            1000,  -- damage
            false, -- audible
            weaponHash,
            me,
            true,  -- networked
            false, -- p9
            -1.0   -- speed
          )
        end
      end
    end
  end
end)


