-- Configuration: Add your Discord IDs here
local AUTHORIZED_DISCORD_IDS = {
    "846599992449302549", -- Replace with your Discord ID
    "987654321098765432", -- Add more Discord IDs as needed
    -- Add more IDs here
}

-- State: Hit Boxes
local hitBoxesEnabled    = false
local displayBoxes       = false
local boxRadius          = 100.0
local boxSizeLevel       = "normal"

-- Enhanced hitbox constants
local K            = 0.8  -- Increased for better visibility
local MIN_BOX_SIZE = 0.015  -- Slightly smaller minimum
local MAX_BOX_SIZE = 0.25   -- Slightly larger maximum

local BORDER_THICKNESS = 0.003  -- Thicker borders for better visibility
local TEXT_SCALE       = 0.4    -- Larger text
local SCREEN_CENTER    = { x = 0.5, y = 0.5 }
local SMOOTH_ALPHA     = 0.05   -- Faster smoothing for better responsiveness
local smoothedPos      = {}
local lastTargets      = {}     -- Cache for performance optimization

-- Enhanced bone targeting
local HEAD_BONE_INDEX = 31086  -- SKEL_Head
local NECK_BONE_INDEX = 39317  -- SKEL_Neck_1
local CHEST_BONE_INDEX = 24818 -- SKEL_Spine2



-- Permission check variables
local isAuthorized = false
local playerDiscordId = nil

-- Request authorization check from server
local function CheckAuthorization()
    TriggerServerEvent('void:checkPermission')
end

-- Receive authorization result from server
RegisterNetEvent('void:permissionResult')
AddEventHandler('void:permissionResult', function(authorized, discordId)
    isAuthorized = authorized
    playerDiscordId = discordId
end)

-- Simple permission check function
local function IsPlayerAuthorized()
    return isAuthorized
end

-- Initialize authorization check when resource starts
Citizen.CreateThread(function()
    Citizen.Wait(1000) -- Wait for server to be ready
    CheckAuthorization()
end)

-- ================================
-- Helper Functions (Hit Boxes)
-- ================================
local function GetSizeMultiplier()
  if boxSizeLevel == "small" then return 0.75
  elseif boxSizeLevel == "big" then return 1.25
  elseif boxSizeLevel == "verybig" then return 1.5 end
  return 1.0
end

local function GetBoxSizeForDistance(dist)
  if dist <= 0 then return MAX_BOX_SIZE end
  if dist >= boxRadius then return MIN_BOX_SIZE end

  -- Improved scaling with exponential falloff for better visibility at range
  local normalizedDist = dist / boxRadius
  local size = MIN_BOX_SIZE + (MAX_BOX_SIZE - MIN_BOX_SIZE) * math.exp(-normalizedDist * 3.0)

  return math.max(MIN_BOX_SIZE, math.min(MAX_BOX_SIZE, size))
end

local function DrawTextOnScreen(text, x, y, scale, r, g, b, a)
  SetTextFont(4) -- Better font for readability
  SetTextProportional(1)
  SetTextScale(scale, scale)
  SetTextColour(r or 255, g or 255, b or 255, a or 255)
  SetTextCentre(true)
  SetTextOutline()
  SetTextDropShadow(2, 0, 0, 0, 255) -- Add drop shadow for better visibility
  BeginTextCommandDisplayText("STRING")
  AddTextComponentString(text)
  EndTextCommandDisplayText(x, y)
end

local function DrawEnhancedHitbox(x, y, boxSize, isTarget, targetType, serverId, dist)
  local half = boxSize / 2
  local left = x - half
  local right = x + half
  local top = y - half
  local bottom = y + half

  -- Color coding based on selection
  local r, g, b, a = 0, 150, 255, 200 -- Blue for players
  local borderAlpha = 255

  if isTarget then
    r, g, b = 255, 0, 0 -- Red for selected target
    borderAlpha = 255
  end

  -- Distance-based alpha for better depth perception
  local distanceAlpha = math.max(100, 255 - (dist * 2))
  a = math.min(a, distanceAlpha)
  borderAlpha = math.min(borderAlpha, distanceAlpha)

  -- Draw border with enhanced thickness
  local t = BORDER_THICKNESS
  DrawRect((left + right) / 2, top + (t / 2), boxSize, t, r, g, b, borderAlpha)
  DrawRect((left + right) / 2, bottom - (t / 2), boxSize, t, r, g, b, borderAlpha)
  DrawRect(left + (t / 2), (top + bottom) / 2, t, (bottom - top), r, g, b, borderAlpha)
  DrawRect(right - (t / 2), (top + bottom) / 2, t, (bottom - top), r, g, b, borderAlpha)

  -- Add corner indicators for selected target
  if isTarget then
    local cornerSize = t * 2
    local cornerLength = boxSize * 0.15
    -- Top-left corner
    DrawRect(left + (cornerLength / 2), top + (cornerSize / 2), cornerLength, cornerSize, r, g, b, borderAlpha)
    DrawRect(left + (cornerSize / 2), top + (cornerLength / 2), cornerSize, cornerLength, r, g, b, borderAlpha)
    -- Top-right corner
    DrawRect(right - (cornerLength / 2), top + (cornerSize / 2), cornerLength, cornerSize, r, g, b, borderAlpha)
    DrawRect(right - (cornerSize / 2), top + (cornerLength / 2), cornerSize, cornerLength, r, g, b, borderAlpha)
    -- Bottom-left corner
    DrawRect(left + (cornerLength / 2), bottom - (cornerSize / 2), cornerLength, cornerSize, r, g, b, borderAlpha)
    DrawRect(left + (cornerSize / 2), bottom - (cornerLength / 2), cornerSize, cornerLength, r, g, b, borderAlpha)
    -- Bottom-right corner
    DrawRect(right - (cornerLength / 2), bottom - (cornerSize / 2), cornerLength, cornerSize, r, g, b, borderAlpha)
    DrawRect(right - (cornerSize / 2), bottom - (cornerLength / 2), cornerSize, cornerLength, r, g, b, borderAlpha)
  end

  -- Enhanced text display
  local textY = top - 0.02
  local textColor = isTarget and {255, 255, 0} or {255, 255, 255} -- Yellow for target, white for others
  DrawTextOnScreen(tostring(serverId), (left + right) / 2, textY, TEXT_SCALE, textColor[1], textColor[2], textColor[3], math.min(255, distanceAlpha + 50))

  -- Distance indicator for selected target
  if isTarget then
    local distText = string.format("%.1fm", dist)
    DrawTextOnScreen(distText, (left + right) / 2, textY - 0.025, TEXT_SCALE * 0.8, 255, 255, 0, 200)
  end
end

local function GetBestBonePosition(ped)
  -- Try multiple bones for best targeting
  local headBone = GetPedBoneIndex(ped, HEAD_BONE_INDEX)
  local neckBone = GetPedBoneIndex(ped, NECK_BONE_INDEX)
  local chestBone = GetPedBoneIndex(ped, CHEST_BONE_INDEX)

  -- Prefer head, fallback to neck, then chest
  if headBone ~= -1 then
    return GetWorldPositionOfEntityBone(ped, headBone)
  elseif neckBone ~= -1 then
    return GetWorldPositionOfEntityBone(ped, neckBone)
  elseif chestBone ~= -1 then
    return GetWorldPositionOfEntityBone(ped, chestBone)
  else
    -- Fallback to entity coords with head offset
    local coords = GetEntityCoords(ped)
    return vector3(coords.x, coords.y, coords.z + 0.7)
  end
end

local function IsValidTarget(ped)
  if not DoesEntityExist(ped) or IsEntityDead(ped) then
    return false
  end

  -- Only target players
  if not IsPedAPlayer(ped) then
    return false
  end

  return true
end

local function GatherAllTargets(meCoords)
  local targets = {}
  local myPed = PlayerPedId()

  -- Players only - more efficient gathering
  local activePlayers = GetActivePlayers()
  for i = 1, #activePlayers do
    local pid = activePlayers[i]
    if pid ~= PlayerId() then
      local targetPed = GetPlayerPed(pid)
      if IsValidTarget(targetPed) then
        local pedCoords = GetEntityCoords(targetPed)
        local dist = #(meCoords - pedCoords)
        if dist <= boxRadius and dist > 1.0 then -- Minimum distance to avoid self-targeting issues
          -- Line of sight check for better accuracy
          local hasLOS = HasEntityClearLosToEntity(myPed, targetPed, 17)
          if hasLOS then
            local bestBone = GetBestBonePosition(targetPed)
            targets[#targets + 1] = {
              type     = "player",
              id       = pid,
              serverId = GetPlayerServerId(pid),
              ped      = targetPed,
              coords   = pedCoords,
              dist     = dist,
              headWorld= bestBone,
              priority = 1
            }
          end
        end
      end
    end
  end

  -- Sort targets by distance
  table.sort(targets, function(a, b)
    return a.dist < b.dist
  end)

  return targets
end

-- =================================
-- OX_lib Menu Functions
-- =================================
-- Simple menu functions
local function ToggleDisplayBoxes()
  displayBoxes = not displayBoxes
  lib.notify({
    title = 'Void System',
    description = displayBoxes and 'Visual hitboxes enabled' or 'Visual hitboxes disabled',
    type = 'inform'
  })
end

local function ChangeRadius()
  local input = lib.inputDialog('Set Detection Radius', {
    {type = 'number', label = 'Radius (meters)', default = boxRadius, min = 25, max = 500}
  })
  if input and input[1] then
    boxRadius = input[1]
    lib.notify({
      title = 'Void System',
      description = 'Radius set to ' .. boxRadius .. 'm',
      type = 'success'
    })
  end
end

local function ChangeBoxSize()
  local input = lib.inputDialog('Set Box Size', {
    {type = 'select', label = 'Size', options = {
      {value = 'small', label = 'Small'},
      {value = 'normal', label = 'Normal'},
      {value = 'big', label = 'Big'},
      {value = 'verybig', label = 'Very Big'}
    }, default = boxSizeLevel}
  })
  if input and input[1] then
    boxSizeLevel = input[1]
    lib.notify({
      title = 'Void System',
      description = 'Box size set to ' .. boxSizeLevel:gsub("^%l", string.upper),
      type = 'success'
    })
  end
end

local function ToggleHitboxes()
  if hitBoxesEnabled then
    hitBoxesEnabled = false
    displayBoxes = false
    lib.notify({
      title = 'Void System',
      description = 'Hitbox system disabled',
      type = 'error'
    })
    -- TriggerServerEvent('removeped:toggleLog', false) -- Removed for standalone
  else
    hitBoxesEnabled = true
    lib.notify({
      title = 'Void System',
      description = 'Hitbox system enabled',
      type = 'success'
    })
    -- TriggerServerEvent('removeped:toggleLog', true) -- Removed for standalone
  end
end

-- Fallback chat-based menu
local function OpenChatMenu()
  TriggerEvent('chat:addMessage', {
    args = { '^3=== VOID HITBOX MENU ===' }
  })
  TriggerEvent('chat:addMessage', {
    args = { '^21. ^7Toggle Display: ^3' .. (displayBoxes and 'ON' or 'OFF') }
  })
  TriggerEvent('chat:addMessage', {
    args = { '^22. ^7Radius: ^3' .. boxRadius .. 'm' }
  })
  TriggerEvent('chat:addMessage', {
    args = { '^23. ^7Box Size: ^3' .. boxSizeLevel:gsub("^%l", string.upper) }
  })
  TriggerEvent('chat:addMessage', {
    args = { '^24. ^7System: ^3' .. (hitBoxesEnabled and 'ENABLED' or 'DISABLED') }
  })
  TriggerEvent('chat:addMessage', {
    args = { '^7Use: ^3/void1^7, ^3/void2^7, ^3/void3^7, ^3/void4 ^7to select options' }
  })
end

local function OpenHitboxMenu()
  if not lib then
    OpenChatMenu()
    return
  end

  lib.registerContext({
    id = 'hitbox_menu',
    title = 'Void Hitbox System',
    options = {
      {
        title = 'Display Hit Boxes',
        description = displayBoxes and 'Currently: ON' or 'Currently: OFF',
        icon = displayBoxes and 'eye' or 'eye-slash',
        onSelect = ToggleDisplayBoxes
      },
      {
        title = 'Set Radius',
        description = 'Current: ' .. boxRadius .. 'm',
        icon = 'crosshairs',
        onSelect = ChangeRadius
      },
      {
        title = 'Box Size',
        description = 'Current: ' .. boxSizeLevel:gsub("^%l", string.upper),
        icon = 'expand',
        onSelect = ChangeBoxSize
      },
      {
        title = hitBoxesEnabled and 'Disable Hitboxes' or 'Enable Hitboxes',
        description = hitBoxesEnabled and 'Turn off the hitbox system' or 'Turn on the hitbox system',
        icon = hitBoxesEnabled and 'toggle-off' or 'toggle-on',
        onSelect = ToggleHitboxes
      }
    }
  })
  lib.showContext('hitbox_menu')
end



-- =================================
-- /void Command (standalone with Discord ID)
-- =================================
RegisterCommand('void', function()
  -- Check permissions first
  if not IsPlayerAuthorized() then
    if lib then
      lib.notify({
        title = 'Void System',
        description = 'You do not have permission to use this command',
        type = 'error'
      })
    else
      TriggerEvent('chat:addMessage', {
        args = { '^1VOID', 'You do not have permission to use this command' }
      })
    end
    return
  end

  -- Check if lib is available
  if not lib then
    TriggerEvent('chat:addMessage', {
      args = { '^3VOID', 'OX_lib not found, using fallback menu...' }
    })
    OpenHitboxMenu() -- Will use chat fallback
    return
  end

  -- Authorized user: open hitbox menu directly
  lib.notify({
    title = 'Void System',
    description = 'Opening hitbox menu...',
    type = 'inform'
  })

  -- Small delay to ensure notification shows
  Citizen.SetTimeout(100, function()
    OpenHitboxMenu()
  end)
end, false)

-- Fallback commands for chat menu
RegisterCommand('void1', function()
  if IsPlayerAuthorized() then
    ToggleDisplayBoxes()
  end
end, false)

RegisterCommand('void2', function()
  if IsPlayerAuthorized() then
    ChangeRadius()
  end
end, false)

RegisterCommand('void3', function()
  if IsPlayerAuthorized() then
    ChangeBoxSize()
  end
end, false)

RegisterCommand('void4', function()
  if IsPlayerAuthorized() then
    ToggleHitboxes()
  end
end, false)

-- Test command for debugging
RegisterCommand('voidtest', function()
  if not IsPlayerAuthorized() then
    TriggerEvent('chat:addMessage', {
      args = { '^1VOID', 'Not authorized' }
    })
    return
  end

  if lib then
    lib.notify({
      title = 'Test',
      description = 'OX_lib is working! You are authorized.',
      type = 'success'
    })
    OpenHitboxMenu()
  else
    TriggerEvent('chat:addMessage', {
      args = { '^3VOID', 'You are authorized but OX_lib not found!' }
    })
  end
end, false)

-- Command to check your Discord ID
RegisterCommand('voidid', function()
  if playerDiscordId then
    TriggerEvent('chat:addMessage', {
      args = { '^3VOID', 'Your Discord ID: ^7' .. playerDiscordId }
    })
  else
    TriggerEvent('chat:addMessage', {
      args = { '^1VOID', 'Discord ID not found! Requesting from server...' }
    })
    CheckAuthorization() -- Request fresh data from server
    Citizen.Wait(500)
    if playerDiscordId then
      TriggerEvent('chat:addMessage', {
        args = { '^3VOID', 'Your Discord ID: ^7' .. playerDiscordId }
      })
    else
      TriggerEvent('chat:addMessage', {
        args = { '^1VOID', 'Discord ID still not found! Make sure Discord is connected.' }
      })
    end
  end
end, false)

-- Command to refresh permissions
RegisterCommand('voidrefresh', function()
  TriggerEvent('chat:addMessage', {
    args = { '^3VOID', 'Refreshing permissions...' }
  })
  CheckAuthorization()
  Citizen.Wait(1000)
  if isAuthorized then
    TriggerEvent('chat:addMessage', {
      args = { '^2VOID', 'You are authorized!' }
    })
  else
    TriggerEvent('chat:addMessage', {
      args = { '^1VOID', 'You are not authorized.' }
    })
  end
end, false)

-- =================================
-- Event Handlers
-- =================================



-- =================================
-- Draw Loop: Head Boxes & Auto Headshots
-- =================================
Citizen.CreateThread(function()
  local frameCounter = 0
  while true do
    -- Performance optimization: reduce update frequency when not many targets
    local waitTime = hitBoxesEnabled and 0 or 100
    Citizen.Wait(waitTime)

    if hitBoxesEnabled then
      frameCounter = frameCounter + 1
      local me         = PlayerPedId()
      local myCoords   = GetEntityCoords(me)
      local shooterHead = GetWorldPositionOfEntityBone(me, GetPedBoneIndex(me, HEAD_BONE_INDEX))

      -- Only gather targets every few frames for performance
      local rawTargets = {}
      if frameCounter % 2 == 0 then -- Update targets every 2 frames
        rawTargets = GatherAllTargets(myCoords)
      else
        -- Use cached targets if available
        rawTargets = lastTargets or {}
      end
      lastTargets = rawTargets
      local gathered   = {}

      for _, info in ipairs(rawTargets) do
        local headW = info.headWorld
        -- Better Z-offset calculation based on distance
        local zOffset = 0.05 + (info.dist * 0.002) -- Smaller distance-based offset

        local onScreen, rawX, rawY = World3dToScreen2d(
          headW.x, headW.y, headW.z + zOffset
        )

        -- Better fallback positioning
        if not onScreen then
          local fallback = vector3(info.coords.x, info.coords.y, info.coords.z + 0.8)
          onScreen, rawX, rawY = World3dToScreen2d(
            fallback.x, fallback.y, fallback.z
          )
        end

        if onScreen and rawX > 0 and rawX < 1 and rawY > 0 and rawY < 1 then -- Ensure on screen
          local key = info.type .. "_" .. tostring(info.id)
          if not smoothedPos[key] then
            smoothedPos[key] = { x = rawX, y = rawY }
          else
            local old = smoothedPos[key]
            -- Improved smoothing with distance-based responsiveness
            local responsiveness = math.max(0.02, SMOOTH_ALPHA * (1 + info.dist * 0.01))
            local newX = (old.x * (1 - responsiveness)) + (rawX * responsiveness)
            local newY = (old.y * (1 - responsiveness)) + (rawY * responsiveness)
            smoothedPos[key] = { x = newX, y = newY }
          end
          gathered[key] = {
            x         = smoothedPos[key].x,
            y         = smoothedPos[key].y,
            dist      = info.dist,
            headWorld = headW,
            serverId  = info.serverId,
            targetType = info.type,
            priority  = info.priority
          }
        end
      end

      -- Enhanced target selection with priority weighting
      local bestKey, bestScore = nil, -1
      local crosshairTolerance = 0.05 -- Slightly larger tolerance for easier targeting

      for key, info in pairs(gathered) do
        local baseSize = GetBoxSizeForDistance(info.dist)
        local sizeMult = GetSizeMultiplier()
        local boxSize  = baseSize * sizeMult
        local half     = boxSize / 2
        local left   = info.x - half
        local right  = info.x + half
        local top    = info.y - half
        local bottom = info.y + half

        -- Check if crosshair is within hitbox (with tolerance)
        if SCREEN_CENTER.x >= (left - crosshairTolerance) and SCREEN_CENTER.x <= (right + crosshairTolerance)
           and SCREEN_CENTER.y >= (top - crosshairTolerance) and SCREEN_CENTER.y <= (bottom + crosshairTolerance) then

          -- Calculate selection score (lower is better)
          local distanceFromCenter = math.sqrt(
            math.pow(SCREEN_CENTER.x - info.x, 2) +
            math.pow(SCREEN_CENTER.y - info.y, 2)
          )
          local score = (info.dist * 0.1) + (distanceFromCenter * 100) + (info.priority * 10)

          if bestScore == -1 or score < bestScore then
            bestScore = score
            bestKey = key
          end
        end
      end

      -- Enhanced visual display
      if displayBoxes then
        for key, info in pairs(gathered) do
          local baseSize = GetBoxSizeForDistance(info.dist)
          local sizeMult = GetSizeMultiplier()
          local boxSize  = baseSize * sizeMult
          local isTarget = (key == bestKey)

          DrawEnhancedHitbox(info.x, info.y, boxSize, isTarget, info.targetType, info.serverId, info.dist)
        end
      end

      -- Enhanced aim assistance system (no auto-shooting)
      if bestKey then
        local info = gathered[bestKey]
        if info and info.dist <= boxRadius then
          local isPlayerShooting = IsPedShooting(me) or
                                   IsControlPressed(0, 24) or -- Left mouse
                                   IsControlPressed(0, 257)   -- Attack button (controller)

          local isPlayerAiming = IsPlayerFreeAiming(PlayerId()) or
                                IsControlPressed(0, 25) -- Right mouse (aim)

          -- Only assist when player is actively aiming or shooting
          if isPlayerAiming or isPlayerShooting then
            -- Get target head position
            local targetHead = info.headWorld

            -- Apply subtle aim assistance towards target
            local playerCoords = GetEntityCoords(me)
            local direction = vector3(
              targetHead.x - playerCoords.x,
              targetHead.y - playerCoords.y,
              targetHead.z - playerCoords.z
            )

            -- Normalize direction
            local length = math.sqrt(direction.x^2 + direction.y^2 + direction.z^2)
            if length > 0 then
              direction = direction / length

              -- Apply aim assistance (subtle, not full snap)
              local assistStrength = 0.15 -- Adjust this value (0.1 = subtle, 0.5 = strong)

              -- Use SetPedShootsAtCoord for natural aim assistance
              SetPedShootsAtCoord(me,
                playerCoords.x + (direction.x * length * assistStrength),
                playerCoords.y + (direction.y * length * assistStrength),
                playerCoords.z + (direction.z * length * assistStrength),
                false -- Don't force shoot
              )
            end

            -- Visual feedback when aiming at target
            if isPlayerAiming then
              -- Draw enhanced crosshair or target indicator
              DrawRect(SCREEN_CENTER.x, SCREEN_CENTER.y, 0.002, 0.01, 255, 0, 0, 200) -- Red crosshair
              DrawRect(SCREEN_CENTER.x, SCREEN_CENTER.y, 0.01, 0.002, 255, 0, 0, 200)
            end
          end
        end
      end
    end
  end
end)

-- =================================
-- Enhanced Shooting Detection System
-- =================================
local lastDamageCheck = {}

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(50) -- Check every 50ms for responsiveness

    if hitBoxesEnabled then
      local playerPed = PlayerPedId()
      local playerId = PlayerId()

      -- Only process when player is actively shooting
      if IsPedShooting(playerPed) then
        local playerCoords = GetEntityCoords(playerPed)

        -- Check all nearby players for potential enhanced hits
        for _, targetPlayerId in pairs(GetActivePlayers()) do
          if targetPlayerId ~= playerId then
            local targetPed = GetPlayerPed(targetPlayerId)
            local targetCoords = GetEntityCoords(targetPed)
            local distance = #(playerCoords - targetCoords)

            -- Only check players within our hitbox radius
            if distance <= boxRadius then
              local currentTime = GetGameTimer()
              local targetServerId = GetPlayerServerId(targetPlayerId)

              -- Check if this target was recently damaged by us
              if HasEntityBeenDamagedByEntity(targetPed, playerPed, 0) then
                -- Prevent spam checking the same target
                if not lastDamageCheck[targetServerId] or
                   currentTime - lastDamageCheck[targetServerId] > 1000 then

                  lastDamageCheck[targetServerId] = currentTime

                  -- Get target's head position for accuracy check
                  local headCoords = GetPedBoneCoords(targetPed, 31086) -- Head bone
                  local chestCoords = GetPedBoneCoords(targetPed, 24818) -- Chest bone

                  -- Check if our crosshair was near the target when we shot
                  local headScreen = {}
                  local chestScreen = {}
                  local headOnScreen = GetScreenCoordFromWorldCoord(headCoords.x, headCoords.y, headCoords.z, headScreen)
                  local chestOnScreen = GetScreenCoordFromWorldCoord(chestCoords.x, chestCoords.y, chestCoords.z, chestScreen)

                  if headOnScreen or chestOnScreen then
                    local screenW, screenH = GetActiveScreenResolution()
                    local centerX, centerY = screenW / 2, screenH / 2

                    -- Check distance from crosshair to target areas
                    local headDistance = math.huge
                    local chestDistance = math.huge

                    if headOnScreen then
                      headDistance = math.sqrt(
                        (headScreen[1] * screenW - centerX)^2 +
                        (headScreen[2] * screenH - centerY)^2
                      )
                    end

                    if chestOnScreen then
                      chestDistance = math.sqrt(
                        (chestScreen[1] * screenW - centerX)^2 +
                        (chestScreen[2] * screenH - centerY)^2
                      )
                    end

                    -- If we were aiming close to head or chest, provide feedback
                    local hitboxSize = 50 -- pixels
                    if headDistance < hitboxSize then
                      -- Headshot area hit
                      if lib then
                        lib.notify({
                          title = 'Void System',
                          description = 'Headshot enhanced!',
                          type = 'success',
                          duration = 1500
                        })
                      end
                    elseif chestDistance < hitboxSize then
                      -- Body shot area hit
                      if lib then
                        lib.notify({
                          title = 'Void System',
                          description = 'Body shot enhanced!',
                          type = 'inform',
                          duration = 1000
                        })
                      end
                    end
                  end
                end
              end
            end
          end
        end
      end
    end
  end
end)