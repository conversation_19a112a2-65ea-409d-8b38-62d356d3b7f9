# Void Hitbox System - Setup Guide

## Discord ID Permission System

This system now uses Discord IDs for permissions instead of ESX groups, making it fully standalone.

## Step 1: Get Your Discord ID

### Method 1: Use the built-in command
1. Join your server
2. Type `/voidid` in chat
3. Copy the Discord ID that appears

### Method 2: Get it from Discord
1. Open Discord
2. Go to Settings → Advanced → Enable Developer Mode
3. Right-click your username → Copy User ID
4. This is your Discord ID (18 digits)

## Step 2: Configure Permissions

Edit the `client.lua` file and find this section at the top:

```lua
-- Configuration: Add your Discord IDs here
local AUTHORIZED_DISCORD_IDS = {
    "846599992449302549", -- Replace with your Discord ID
    "987654321098765432", -- Add more Discord IDs as needed
    -- Add more IDs here
}
```

Replace the example IDs with real Discord IDs:

```lua
local AUTHORIZED_DISCORD_IDS = {
    "YOUR_DISCORD_ID_HERE",
    "FRIEND_DISCORD_ID_HERE",
    "ADMIN_DISCORD_ID_HERE",
    -- Add as many as you need
}
```

## Step 3: Installation

1. Make sure `ox_lib` is installed and started before this resource
2. Add to your server.cfg:
   ```
   ensure ox_lib
   ensure void_syko_hitbox
   ```
3. Restart the server or use `/refresh` and `/ensure void_syko_hitbox`

## Step 4: Testing

1. Join the server
2. Type `/voidtest` to check if everything works
3. Type `/void` to open the menu
4. If the menu doesn't work, it will show a chat-based fallback

## Commands

### Main Commands
- `/void` - Open hitbox menu (or show chat menu if OX_lib fails)
- `/voidtest` - Test if system is working
- `/voidid` - Show your Discord ID

### Fallback Commands (if OX_lib menu fails)
- `/void1` - Toggle display hitboxes
- `/void2` - Change radius
- `/void3` - Change box size
- `/void4` - Enable/disable system

## Features

### Hitbox System
- **Player targeting only** (no NPCs for better performance)
- **Visual hitboxes** with blue boxes (red when selected)
- **Auto-aim and shoot** with left-click
- **Configurable radius** (25-500 meters)
- **Multiple box sizes** (Small, Normal, Big, Very Big)

### Permission System
- **Discord ID based** - no ESX required
- **Multiple users** - add as many Discord IDs as needed
- **Secure** - only authorized users can access

## Troubleshooting

### "You do not have permission"
1. Check your Discord ID with `/voidid`
2. Make sure it's added to the AUTHORIZED_DISCORD_IDS list
3. Restart the resource after adding IDs

### "Discord ID not found"
1. Make sure Discord is connected to FiveM
2. Check server.cfg for `set steam_webApiKey` (sometimes needed)
3. Restart FiveM and try again

### Menu not opening
1. Try `/voidtest` first
2. If OX_lib fails, use `/void` for chat menu
3. Use individual commands: `/void1`, `/void2`, etc.

### No hitboxes showing
1. Enable "Display Hit Boxes" in menu
2. Make sure system is enabled
3. Check that other players are nearby
4. Try different radius settings

## Example Configuration

For a server with 3 admins:

```lua
local AUTHORIZED_DISCORD_IDS = {
    "123456789012345678", -- Admin 1
    "234567890123456789", -- Admin 2  
    "345678901234567890", -- Admin 3
}
```

## Security Notes

- Discord IDs are public information, but still more secure than open commands
- Only people with Discord connected can be authorized
- You can easily add/remove access by editing the list
- No database required - everything is in the script

## Performance

- **Standalone** - no ESX dependency
- **Lightweight** - only processes players, no NPCs
- **Optimized** - cached targeting every 2 frames
- **Efficient** - line of sight checks for accuracy

## Support

If you need help:
1. Check the TROUBLESHOOTING.md file
2. Use `/voidtest` to diagnose issues
3. Check server console for errors
4. Make sure ox_lib is properly installed
