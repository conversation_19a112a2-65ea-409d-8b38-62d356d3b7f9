# Void Hitbox System - Enhanced Version

## Overview
This is an enhanced version of the Void hitbox system that has been completely refactored to use OX_lib instead of HTML/CSS/JS, with significant improvements to hitbox accuracy, performance, and visual feedback.

## Key Improvements

### 1. UI System Replacement
- **Removed**: HTML, CSS, and JavaScript files
- **Added**: OX_lib menu system with modern, responsive menus
- **Benefits**: Better integration with FiveM, reduced resource usage, more reliable

### 2. Enhanced Hitbox System
- **Improved Targeting**: Multi-bone targeting (head, neck, chest) with fallbacks
- **Better Accuracy**: Enhanced distance calculations with exponential falloff
- **Line of Sight**: Added LOS checks for more realistic targeting
- **Priority System**: Players have higher priority than NPCs

### 3. Visual Enhancements
- **Color Coding**: Different colors for players (blue), NPCs (orange), and selected targets (red)
- **Enhanced Borders**: Thicker, more visible borders with corner indicators for selected targets
- **Distance Indicators**: Shows distance to selected target
- **Improved Text**: Better font, drop shadows, and positioning

### 4. Performance Optimizations
- **Frame Limiting**: Reduced update frequency when not active
- **Target Caching**: Targets updated every 2 frames instead of every frame
- **NPC Limiting**: Maximum 20 NPCs processed for performance
- **Better Filtering**: Skip invalid targets (animals, drivers, etc.)

### 5. User Experience
- **OX_lib Notifications**: Modern notification system
- **Input Dialogs**: Clean input dialogs for settings
- **Menu Persistence**: Settings are remembered during session
- **Better Feedback**: Visual and audio feedback for actions

## Installation

1. Ensure you have `ox_lib` installed and running
2. Replace your existing void hitbox files with these enhanced versions
3. Restart the resource

## Usage

### Commands
- `/void` - Opens the main menu (owner permission required)

### Menu Options

#### Hit Boxes Configuration
- **Display Hit Boxes**: Toggle visual hitbox display
- **Include NPCs**: Include NPC peds in targeting
- **Radius**: Set detection radius (25-500m)
- **Box Size**: Choose hitbox size (Small, Normal, Big, Very Big)
- **Toggle System**: Enable/disable the entire hitbox system

#### Interior Hold Configuration
- **Ped Count**: Number of hostile peds (5, 10, 15, 20)
- **Weapon Type**: Pistol or Automatic weapons for peds
- **Location**: Choose from predefined locations
- **Start**: Begin the interior hold challenge

## Features

### Hitbox System
- **Multi-bone targeting** with intelligent fallbacks
- **Line of sight checks** for realistic targeting
- **Priority-based selection** (players over NPCs)
- **Distance-based scaling** with improved visibility
- **Enhanced visual feedback** with color coding
- **Smooth position interpolation** for stable targeting

### Interior Hold
- **Configurable difficulty** with ped count and weapon selection
- **Multiple locations** with predefined coordinates
- **Countdown system** with notifications
- **Win/lose detection** with cleanup
- **Enhanced AI** with proper relationship groups

## Technical Details

### Dependencies
- ESX Framework
- ox_lib

### Performance
- Optimized target gathering (every 2 frames)
- Limited NPC processing (max 20)
- Efficient line of sight checks
- Smart caching system

### Compatibility
- Compatible with most ESX servers
- Requires ox_lib for menu system
- No HTML/NUI dependencies

## Configuration

All settings can be configured through the in-game menu system. Settings are persistent during the session but reset on resource restart.

## Troubleshooting

1. **Menu not opening**: Ensure you have owner permissions in ESX
2. **Hitboxes not showing**: Check that "Display Hit Boxes" is enabled
3. **Poor performance**: Reduce radius or disable NPC inclusion
4. **OX_lib errors**: Ensure ox_lib is properly installed and started before this resource

## Credits
- Original concept by syko
- Enhanced by AI Assistant
- Uses ox_lib by Overextended
