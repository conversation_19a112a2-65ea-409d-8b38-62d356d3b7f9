# Void Hitbox System - Standalone Version

## Overview
This is a standalone version of the Void hitbox system that uses Discord ID permissions instead of ESX groups. Completely refactored to use OX_lib instead of HTML/CSS/JS, with significant improvements to hitbox accuracy, performance, and visual feedback. **Player targeting only** - no NPC functionality.

## Key Improvements

### 1. UI System Replacement
- **Removed**: HTML, CSS, and JavaScript files
- **Added**: OX_lib menu system with clean, responsive interface
- **Benefits**: Better integration with FiveM, reduced resource usage, more reliable

### 2. Enhanced Hitbox System
- **Improved Targeting**: Multi-bone targeting (head, neck, chest) with fallbacks
- **Better Accuracy**: Enhanced distance calculations with exponential falloff
- **Line of Sight**: Added LOS checks for more realistic targeting
- **Player Focus**: Targets players only for better performance and accuracy

### 3. Visual Enhancements
- **Color Coding**: Blue for players, red for selected targets
- **Enhanced Borders**: Thicker, more visible borders with corner indicators for selected targets
- **Distance Indicators**: Shows distance to selected target
- **Improved Text**: Better font, drop shadows, and positioning

### 4. Performance Optimizations
- **Frame Limiting**: Reduced update frequency when not active
- **Target Caching**: Targets updated every 2 frames instead of every frame
- **Player Only**: No NPC processing for maximum performance
- **Better Filtering**: Skip invalid targets efficiently

### 5. User Experience
- **OX_lib Notifications**: Modern notification system
- **Input Dialogs**: Clean input dialogs for settings
- **Menu Persistence**: Settings are remembered during session
- **Direct Access**: `/void` command opens hitbox menu directly
- **Standalone**: No ESX dependency - uses Discord ID permissions

## Installation

1. Ensure you have `ox_lib` installed and running
2. Configure Discord IDs in `client.lua` (see SETUP.md)
3. Add to server.cfg: `ensure ox_lib` then `ensure void_syko_hitbox`
4. Restart the server

## Usage

### Commands
- `/void` - Opens the hitbox menu (Discord ID permission required)
- `/voidid` - Shows your Discord ID for configuration
- `/voidtest` - Tests if the system is working

### Menu Options

#### Hit Boxes Configuration
- **Display Hit Boxes**: Toggle visual hitbox display
- **Radius**: Set detection radius (25-500m)
- **Box Size**: Choose hitbox size (Small, Normal, Big, Very Big)
- **Toggle System**: Enable/disable the entire hitbox system

## Features

### Hitbox System
- **Multi-bone targeting** with intelligent fallbacks (head → neck → chest)
- **Line of sight checks** for realistic targeting
- **Player-only targeting** for optimal performance
- **Distance-based scaling** with improved visibility
- **Enhanced visual feedback** with color coding
- **Smooth position interpolation** for stable targeting
- **Direct menu access** via `/void` command

## Technical Details

### Dependencies
- ox_lib (required)
- Discord connection (for permissions)

### Performance
- Optimized target gathering (every 2 frames)
- Player-only processing for maximum efficiency
- Efficient line of sight checks
- Smart caching system

### Compatibility
- **Standalone** - works on any FiveM server
- Requires ox_lib for menu system
- No ESX or other framework dependencies
- No HTML/NUI dependencies

## Configuration

### Discord ID Setup
1. Get your Discord ID using `/voidid` command
2. Edit `client.lua` and add your Discord ID to the `AUTHORIZED_DISCORD_IDS` table
3. Restart the resource

### In-Game Settings
All settings can be configured through the in-game menu system. Settings are persistent during the session but reset on resource restart.

## Troubleshooting

1. **Menu not opening**: Check your Discord ID is in the authorized list
2. **"No permission" error**: Use `/voidid` to get your Discord ID and add it to client.lua
3. **Hitboxes not showing**: Check that "Display Hit Boxes" is enabled
4. **Poor performance**: Reduce radius setting
5. **OX_lib errors**: Ensure ox_lib is properly installed and started before this resource

## Credits
- Original concept by syko
- Enhanced by AI Assistant
- Uses ox_lib by Overextended
